{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test:auth": "node scripts/test-auth-node.js", "test:auth:files": "node scripts/test-auth-node.js --test=files", "test:auth:quality": "node scripts/test-auth-node.js --test=quality", "test:auth:api": "node scripts/test-auth-node.js --test=api", "test:auth:components": "node scripts/test-auth-node.js --test=components", "test:auth:docs": "node scripts/test-auth-node.js --test=docs"}, "dependencies": {"@react-oauth/google": "^0.12.1", "@tailwindcss/line-clamp": "^0.4.4", "@tailwindcss/postcss": "^4.0.15", "axios": "^1.8.3", "clsx": "^2.1.1", "hls.js": "^1.6.2", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "next": "^15.3.1", "react": "^19.0.0", "react-cookie": "^8.0.1", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-select": "^5.10.1", "react-spinners": "^0.16.1", "slugify": "^1.6.6", "swiper": "^11.2.6", "universal-cookie": "^8.0.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/typography": "^0.5.16", "@types/node": "22.15.23", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.2.3", "postcss": "^8.5.3", "postcss-custom-media": "^11.0.5", "tailwindcss": "^3.4.17"}}