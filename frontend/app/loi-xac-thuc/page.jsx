"use client";

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';

export default function AuthErrorPage() {
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          <Image
            src="/images/Logo.png"
            alt="Logo"
            width={120}
            height={48}
            className="h-12 w-auto"
          />
        </div>
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Lỗi Xác Thực
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          Có lỗi xảy ra trong quá trình xác thực
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
              <svg
                className="h-6 w-6 text-red-600"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>
            
            <h3 className="mt-4 text-lg font-medium text-gray-900">
              Phiên đăng nhập không hợp lệ
            </h3>
            
            <p className="mt-2 text-sm text-gray-500">
              Phiên đăng nhập của bạn đã hết hạn hoặc không hợp lệ. 
              Vui lòng đăng nhập lại để tiếp tục sử dụng dịch vụ.
            </p>

            <div className="mt-6 space-y-3">
              <Link
                href="/dang-nhap"
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#299D55] hover:bg-[#198C43] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#299D55]"
              >
                Đăng nhập lại
              </Link>
              
              <Link
                href="/"
                className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#299D55]"
              >
                Về trang chủ
              </Link>
            </div>

            <div className="mt-6 text-center">
              <p className="text-xs text-gray-500">
                Nếu vấn đề vẫn tiếp tục, vui lòng liên hệ{' '}
                <a
                  href="tel:0828949479"
                  className="text-[#299D55] hover:text-[#198C43]"
                >
                  hỗ trợ: 0828 949 479
                </a>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
